<?php

namespace App\Console\Commands;

use App\Data\Mapping;
use App\IntegrationMapper\ProviderRegistry;
use App\Rules\NotExists;
use App\Services\APIS\ExactGlobeAPIService;
use App\Services\DataMapperService;
use Illuminate\Console\Command;

class TestCommand extends Command
{
    protected $signature = 'app:test';

    public function handle(): void
    {
//        $registry = new ProviderRegistry();
//
//        $provider = $registry->getProvider('exact');
//
//        $provider->fromArray([
//            'name' => '<PERSON>',
//            'email' => '<EMAIL>',
//            'phone' => '**********',
//            'country' => 'NL',
//            'city' => 'Amsterdam',
//            'postalCode' => '1011 AA',
//            'address' => 'Kerkstraat 1',
//            'debitorNumber' => '********',
//            'creditorNumber' => '********',
//        ]);
//
//        $provider->toDatabase();

        $service = new ExactGlobeAPIService();
        $account = $service->getAccount('C7E48EB5-7470-4D3D-B291-059905DECF87');
        /** @var Mapping $x */
        $x = new Mapping(
            localKey: 'exact_uniq_id',
            remoteKey: 'exact_uniq_id',
            rules: [
                'required',
                'string',
                new NotExists('companies', 'exact_uniq_id'),
            ],
        );
        $y = [
            'exact_uniq_id' => 'C7E48EB5-7470-4D3D-B291-059905DECF87',
            'country_id' => 152,
            'locale_id' => 1,
            'name' => 'Autoschade Pijnaker',
            'email' => null,
        ];
        dd($x);

        $mappings = [
            'exact_uniq_id' => [
                'key' => 'exact_uniq_id',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'exact_uniq_id'),
                ],
            ],
            'country_id' => [
                'key' => 'country_code',
                'rules' => [
                    'required',
                    'integer',
                    'exists:countries,id',
                ],
                'before' => [
                    \App\Transformers\CountryCodeToCountryIdTransformer::class,
                ],
            ],
            'locale_id' => [
                'key' => 'country_code',
                'rules' => [
                    'required',
                    'integer',
                    'exists:locales,id',
                ],
                'before' => [
                    \App\Transformers\CountryCodeToLocaleIdTransformer::class,
                ],
                'default' => 1,
            ],
            'name' => [
                'key' => 'name',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'name'),
                ],
            ],
            'email' => [
                'key' => 'email',
                'rules' => [
                    'nullable',
                    'email:rfc,dns,spoof',
                ],
            ],
        ];

        $companyMapper = new DataMapperService(
            $mappings,
            $account
        );
        dump(
            $companyMapper->validated(),
            $companyMapper->errors->toArray()
        );
//        expect($companyMapper->validated())->toMatchArray([
//            'exact_uniq_id' => 'C7E48EB5-7470-4D3D-B291-059905DECF87',
//            'country_id' => 152,
//            'locale_id' => 1,
//            'name' => 'Autoschade Pijnaker',
//            'email' => null,
//        ]);
    }
}
