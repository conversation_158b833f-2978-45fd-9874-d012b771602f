<?php

namespace App\Http\Controllers\Admin;

use App\Models\Company;
use App\Models\Country;
use App\Services\APIS\ExactGlobeAPIService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Session;

class SuppliersController extends CompaniesController
{
    public function search(
        Request $request,
        ExactGlobeAPIService $exactGlobe
    ): View|RedirectResponse {

        if (! $request->has('query')) {
            return view('admin::pages.companies.import');
        }

        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:255',
        ], [
            'query' => 'The search term must consist of 3 to a maximum of 255 characters',
        ]);

        if ($validator->fails() === false) {

            $results = $exactGlobe->searchAccounts($validator->validated()['query']);

            if ($results === false) {
                $validator->errors()->add(
                    'exactGlobve',
                    'Could not query ExactGlobe data'
                );
            }
        }

        if (count($validator->errors()) > 0) {
            return redirect()
                ->route('admin.suppliers.import.search')
                ->withInput()
                ->withErrors($validator)
            ;
        }

        Session::flashInput($request->input());

        $countries = Country::all();

        $existingSuppliers = Company::query()
            ->where('is_supplier', true)
            ->select('exact_uniq_id')
            ->get()
            ->pluck('exact_uniq_id')
            ->toArray()
        ;

        $results = $results
            ->filter(function ($item) use ($existingSuppliers) {
                return ! in_array($item->exact_uniq_id, $existingSuppliers);
            })
            ->map(function ($item) use ($countries) {
                $item->country = $countries->where(
                    'code',
                    strtoupper($item->country_code)
                )?->first();

                return $item;
            }
        );

        return view('admin::pages.companies.import', [
            'data' => $results,
        ]);
    }
}
