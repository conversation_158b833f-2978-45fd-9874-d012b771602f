<?php

namespace App\Http\Controllers\Admin;

use App\Models\Company;
use App\Models\Country;
use App\Rules\NotExists;
use App\Services\APIS\ExactGlobeAPIService;
use App\Services\DataMapperService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Session;

class SuppliersController extends CompaniesController
{
    public function import(
        string $uniqId,
        ExactGlobeAPIService $exactGlobe
    ): View|RedirectResponse {
        // get the supplier data
        if (($account = $exactGlobe->getAccount($uniqId)) === null) {
            session()->flash('error', 'The Exact Globe Account cannot be found.');

            return to_route('admin.suppliers.import.search');
        }

        $mappings = [
            'exact_uniq_id' => [
                'key' => 'exact_uniq_id',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'exact_uniq_id'),
                ],
            ],
            'country_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:countries,id',
                'before' => [
                    \App\Transformers\CountryCodeToCountryIdTransformer::class,
                ],
            ],
            'locale_id' => [
                'key' => 'country_code',
                'rules' => 'required|integer|exists:locales,id',
                'before' => [
                    \App\Transformers\CountryCodeToLocaleIdTransformer::class,
                ],
                'default' => 1,
            ],
            'name' => [
                'key' => 'name',
                'rules' => [
                    'required',
                    'string',
                    new NotExists('companies', 'name'),
                ],
            ],
            'email' => [
                'key' => 'email',
                'rules' => 'nullable|email:rfc,dns,spoof',
            ],
        ];

        $companyMapper = new DataMapperService(
            $mappings,
            $account
        );

        if ($companyMapper->hasErrors()) {
            return redirect()
                ->route('admin.suppliers.import.search')
                ->withErrors($companyMapper->errors)
            ;
        }

        $validated = $companyMapper->validated();

        $company = Company::create([
            'locale_id' => $validated['locale_id'],
            'country_id' => $validated['country_id'],
            'exact_uniq_id' => $validated['exact_uniq_id'],
            'name' => $validated['name'],
            'email' => $validated['email'],
            'is_supplier' => true,
        ]);

        session()->flash('message', 'The supplier has been imported');

        return to_route('admin.suppliers.edit', $company->id);
    }

    public function search(
        Request $request,
        ExactGlobeAPIService $exactGlobe
    ): View|RedirectResponse {

        if (! $request->has('query')) {
            return view('admin::pages.companies.import');
        }

        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:3|max:255',
        ], [
            'query' => 'The search term must consist of 3 to a maximum of 255 characters',
        ]);

        if ($validator->fails() === false) {

            $results = $exactGlobe->searchAccounts($validator->validated()['query']);

            if ($results === false) {
                $validator->errors()->add(
                    'exactGlobve',
                    'Could not query ExactGlobe data'
                );
            }
        }

        if (count($validator->errors()) > 0) {
            return redirect()
                ->route('admin.suppliers.import.search')
                ->withInput()
                ->withErrors($validator)
            ;
        }

        Session::flashInput($request->input());

        $countries = Country::all();

        $existingSuppliers = Company::query()
            ->where('is_supplier', true)
            ->select('exact_uniq_id')
            ->get()
            ->pluck('exact_uniq_id')
            ->toArray()
        ;

        $results = $results
            ->filter(function ($item) use ($existingSuppliers) {
                return ! in_array($item->exact_uniq_id, $existingSuppliers);
            })
            ->map(function ($item) use ($countries) {
                $item->country = $countries->where(
                    'code',
                    strtoupper($item->country_code)
                )?->first();

                return $item;
            }
        );

        return view('admin::pages.companies.import', [
            'data' => $results,
        ]);
    }
}
