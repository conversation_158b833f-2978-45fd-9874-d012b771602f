<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AddressType;
use App\Http\Controllers\ModuleController;
use App\Http\Requests\companies\StoreCompanyRequest;
use App\Models\Address;
use App\Models\Admin;
use App\Models\Company;
use App\Models\Country;
use App\Models\RelatedAddress;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\App;
use Str;

class CompaniesController extends ModuleController
{
    public function index(): View
    {
        return view('admin::pages.companies.index', [
            'data' => Company::query()
                ->where('is_' . Str::singular(App::get('module')), 1)
                ->withCount('users', 'items')
                ->with('country')
                ->withIsLocked()
                ->applyFilter()
                ->applySort()
                ->get(),
        ]);
    }

    public function create(Company $company): View
    {
        $company->uniq_id = Str::uniqId();

        if ($this->module == 'suppliers') {
            $company->is_supplier = true;
        }
        if ($this->module == 'customers') {
            $company->is_customer = true;
        }

        return $this->edit($company);
    }

    public function edit(Company $company): View
    {
        $company->load([
            'taxIdentifier',
            'addresses',
            'addresses.address',
        ]);

        $employees = Admin::query()
            ->where('is_backoffice', 1)
            ->get()
        ;

        $countries = Country::all();

        return view('admin::pages.companies.create', [
            'data' => $company,
            'countries' => $countries,
            'usedCountries' => $countries->whereIn('id', array_column(config('modules.products.pricing.countries', []), 'id')),
            'employees' => $employees,
        ]);
    }

    public function store(
        StoreCompanyRequest $request
    ): View {

        $validated = $request->validated();

        $validated['is_customer'] ??= false;
        $validated['is_supplier'] ??= false;

        $model = Company::updateOrCreate(
            [
                'uniq_id' => $validated['uniq_id'],
            ], $validated
        );

        foreach ([
            'invoice',
            'delivery',
        ] as $addressType) {

            if (! isset($validated['addresses'][$addressType])) {
                continue;
            }

            $addressData = $validated['addresses'][$addressType];

            $address = Address::updateOrCreate([
                'postalcode' => $addressData['postalcode'] ?? '',
                'city' => $addressData['city'] ?? '',
                'street' => $addressData['street'] ?? '',
                'housenumber' => $addressData['housenumber'] ?? '',
                'addition' => $addressData['addition'] ?? '',
                'department' => $addressData['department'] ?? '',
                'country_id' => $validated['country_id'],
                'state' => $addressData['state'] ?? '',
            ], [
                'verified' => 0,
            ]);

            $relatedAddress = RelatedAddress::updateOrCreate([
                'relatable_type' => $model->getMorphClass(),
                'relatable_id' => $model->id,
                'type' => AddressType::from($addressType),
                'is_default' => true,
            ], [
                'address_id' => $address->id,
                'company' => $model->name,
            ]);

            // delete old related addresses
        }

        session()->flash('message', $model->wasRecentlyCreated
            ? 'Company created successfully'
            : 'Company updated successfully'
        );

        return self::index();
    }

    public function createOrUpdateAddress($model, $request, $addressType, $type, $addressId = null)
    {

        if (empty($addressId)) {
            $addressId = $request->input($addressType . '_address_id');
        }

        $addressData = [
            'postalcode' => $request->input('address_' . $addressType . '_postal_code'),
            'city' => $request->input('address_' . $addressType . '_city'),
            'street' => $request->input('address_' . $addressType . '_street'),
            'housenumber' => $request->input('address_' . $addressType . '_housenumber'),
            'addition' => $request->input('address_' . $addressType . '_addition') ?? '',
            'department' => $request->input('address_' . $addressType . '_department') ?? '',
            'country_id' => $request->input('country_id'),
            'verified' => 1,
        ];

        // $address = Address::find($addressId);

        // if ($address) {
        //     $address->update($addressData);
        // } else {
        //     $address = Address::create($addressData);
        // }        $id = $request->input('id');

        $address = Address::findOrNew($addressId);
        $address->fill($addressData);
        $address->save();

        $addressId = $address->id;

        $model->addresses()->attach($address->id, [
            'type' => $type,
            'language' => $request->input('address_' . $addressType . '_country'),
            'company' => $request->input('name'),
            'email' => $request->input('address_' . $addressType . '_email'),
            'phone' => $request->input('address_' . $addressType . '_phone_number'),
            'is_default' => 1,
        ]);

        if ($addressType == 'invoice') {
            if ($request->has('address_invoice_contacts')) {
                $companyAddress = $model->addresses()->wherePivot('type', $addressType)->first()->pivot;

                foreach ($request->input('address_invoice_contacts') as $contactId) {
                    $user = User::find($contactId);

                    $model->addressesUsers()->newPivotStatement()
                        ->where('user_id', $contactId)
                        ->delete();

                    if ($user) {
                        $user->addressesCompanies()->attach($contactId, ['company_address_id' => $companyAddress->id]);
                    }
                }
            }
        }

        return $addressId;
    }

    public function destroy(Company $data): View
    {
        $data->delete();
        session()->flash('message', 'Company deleted successfully');

        return self::index();
    }
}
