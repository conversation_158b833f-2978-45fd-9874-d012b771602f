<?php

namespace App\Http\Controllers\Admin;

use App\Enums\SystemContext;
use App\Http\Controllers\ModuleController;
use App\Http\Requests\StoreItemRequest;
use App\Models\Company;
use App\Models\Item;
use App\Models\ItemGroup;
use App\Models\Property;
use App\Models\Unit;
use App\Models\UnitGroup;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SuppliersItemsController extends ModuleController
{
    public function index(
        ?Company $company
    ): View {
        $items = Item::query()
            ->whereNotNull('items.item_related_id')
            ->withCount('products')
            ->with([
                'company:id,name',
                'related',
                'related.group:id,systemname',
                'related.units',
                'related.units.unitGroupUnit',
                'related.units.unitGroupUnit.unit',
                'units',
                'units.unitGroupUnit',
                'units.unitGroupUnit.unit',
            ])
            ->withIsLocked()
            ->applyFilter()
            ->applySort()
            ->get()
        ;

        return view('admin::pages.suppliersitems.index', [
            'company' => $company,
            'data' => $items,
        ]);
    }

    public function create(
        Company $company
    ): View {
        return $this->edit($company, new Item());
    }

    public function edit(
        Company $company,
        Item $item
    ): View|RedirectResponse {
        $item->load([
            'rules',
            'units',
            'units.relations',
            'units.unitGroupUnit',
            'units.unitGroupUnit.unit',
            'units.unitGroupUnit.unit.compositionals',
            'variants',
            'variants.units',
            'variants.units.unitGroupUnit',
            'variants.units.unitGroupUnit.unit',
            'variants.units.unitGroupUnit.unit.compositionals',
            'variants.units.relations',
            'variants.units.relations.relatable',
            'ledgerAccounts',
        ]);

        $properties = Property::query()
            ->where('system_context', SystemContext::UNIT)
            ->with('values')
            ->get()
        ;

        $unitGroups = UnitGroup::query()
            ->whereNot('value', 'weight')
            ->with([
                'units',
                'units.unit',
                'units.unit.compositionals',
            ])
            ->get()
        ;

        $unitGroupWeightUnits = Unit::query()
            ->whereNot('unit_type_id', 4)
            ->get()
        ;

        $itemsRelated = Item::query()
            ->where('company_id', Company::main())
            ->with([
                'group',
                'units',
                'units.relations',
                'units.unitGroupUnit',
                'units.unitGroupUnit.unit',
                'units.unitGroupUnit.unit.compositionals',
            ])
            ->withIsLocked()
            ->get()
        ;

        $this->bladeService->addToDataSet('item', [$item]);
        $this->bladeService->addToDataSet('properties', $properties);
        $this->bladeService->addToDataSet('unit_groups', $unitGroups);
        $this->bladeService->addToDataSet('itemsRelated', $itemsRelated);

        return view('admin::pages.suppliersitems.create', [
            'data' => $item,
            'company' => $company,
            'companies' => ! $company->exists
                ? Company::query()
                    ->where('is_supplier', true)
                    ->get()
                : null,

            'unitGroups' => $unitGroups,
            'unitGroupWeightUnits' => $unitGroupWeightUnits,
            'properties' => $properties,
            'itemGroups' => ItemGroup::all(),
            'itemsRelated' => $itemsRelated,
        ]);
    }

    public function store(
        StoreItemRequest $request
    ): RedirectResponse {
        $validated = $request->validated();

        // dd($validated);

        $item = DB::transaction(function () use ($validated) {

            $itemRelated = Item::query()
                ->where('id', $validated['item_related_id'])
                ->select('item_group_id', 'type')
                ->with('variants:id,item_id')
                ->first()
            ;

            // create item
            $item = Item::query()
                ->withIsLocked()
                ->updateOrCreate([
                'id' => $validated['id'],
            ], [
                'company_id' => $validated['company_id'],
                'item_group_id' => $itemRelated?->item_group_id ?? null,
                'item_related_id' => $validated['item_related_id'],
                'code' => $validated['code'],
                'systemname' => $validated['systemname'],
                'type' => $itemRelated?->type ?? null,
                'is_custom' => false, // TODO: is for if item is directy 'custom' added/created at purchaseorder
            ]);

            $itemRuleIds = [];

            // loop through all item rules and create them if type = item
            foreach ($validated['rules'] ?? [] as $ruleId => $rule) {

                if ($rule['apply_to'] != 'item') {
                continue;
                }

                $itemRule = $item->rules()
                    ->withIsLocked()
                    ->updateOrCreate([
                    'id' => strpos($ruleId, '-') !== false ? null : $ruleId,
                ], [
                    'unit_group_id' => $rule['unit_group_id'],
                    'type' => $rule['type'],
                    'unit_specification' => $rule['unit_specification'],
                    'unit_specification_min' => $rule['unit_specification_min'],
                    'unit_specification_max' => $rule['unit_specification_max'],
                    'pricing_type' => $rule['pricing_type'],
                    'pricing_value' => Str::startsWith($rule['pricing_type'], 'percentage_')
                        ? $rule['pricing_percentage_value']
                        : $rule['pricing_price_value'],
                    'is_active' => $rule['is_active'] == '1' ? true : false,
                ]);

                $itemRuleIds[] = $itemRule->id;
            }

            $itemUnitIds = [];
            foreach ($validated['units'] as $unit) {

                // create item unit
                $itemUnit = $item
                    ->units()
                    ->withIsLocked()
                    ->updateOrCreate([
                        'id' => strpos($unit['id'], '-') !== false ? null : $unit['id'],
                    ], [
                        'unit_group_id' => $unit['unit_group_id'],
                        'unit_group_unit_id' => $unit['unit_group_unit_id'],
                        'value' => $unit['value'] ?? null,
                    ])
                ;
                $itemUnitIds[] = $itemUnit->id;

                // item unit relations
                $itemUnitRelationIds = [];
                foreach (($unit['relations'] ?? []) as $relation) {

                    if (is_null($relation) || is_null($relation['model'])) {
                    continue;
                    }

                    [$relatableType, $relatableId] = explode('::', $relation['model']);

                    // create item unit relation
                    $itemUnitRelation = $itemUnit->relations()
                        ->withIsLocked()
                        ->updateOrCreate([
                            'id' => strpos($relation['id'], '-') !== false ? null : $relation['id'],
                        ], [
                            'relatable_type' => $relatableType,
                            'relatable_id' => intval($relatableId),
                        ])
                    ;

                    $itemUnitRelationIds[] = $itemUnitRelation->id;
                }

                // delete itemUnitRelations that are not in the request
                $itemUnit->relations()
                    ->withIsLocked()
                    ->whereNotIn('id', $itemUnitRelationIds)
                    ->delete()
                ;
            }

            // delete itemUnits that are not in the request
            $item->units()
                ->withIsLocked()
                ->whereNotIn('id', $itemUnitIds)
                ->delete()
            ;

            // item variants
            $itemVariantIds = [];
            foreach ($validated['variants'] as $variant) {

                $itemVariant = $item->variants()
                    ->withIsLocked()
                    ->updateOrCreate([
                        'id' => strpos($variant['id'], '-') !== false ? null : $variant['id'],
                    ], [
                        'code' => $variant['code'],
                        'systemname' => $variant['name'],
                        'item_variant_related_id' => $itemRelated?->variants?->first()?->id,
                        // 'is_active' => $variant['is_active'] == '1' ? true : false,
                    ])
                ;

                $itemVariantIds[] = $itemVariant->id;

                $itemVariantUnitIds = [];
                foreach ($variant['units'] as $unitGroupId => $unit) {

                    $itemVariantUnit = $itemVariant->units()
                        ->withIsLocked()
                        ->updateOrCreate([
                            'id' => strpos($unit['id'], '-') !== false ? null : $unit['id'],
                        ], [
                            'unit_group_id' => $unitGroupId,
                            'unit_group_unit_id' => $validated['units'][$unitGroupId]['unit_group_unit_id'],
                            'value' => ($unit['value'] ?? null),
                        ])
                    ;

                    $itemVariantUnitIds[] = $itemVariantUnit->id;

                    $itemVariantUnitRelationIds = [];
                    foreach (($unit['relations'] ?? []) as $relation) {

                        [$relatableType, $relatableId] = explode('::', $relation['model']);

                        $itemVariantUnitRelation = $itemVariantUnit->relations()
                            ->withIsLocked()
                            ->updateOrCreate([
                                'id' => strpos($relation['id'], '-') !== false ? null : $relation['id'],
                            ], [
                                'relatable_type' => $relatableType,
                                'relatable_id' => intval($relatableId),
                            ])
                        ;

                        $itemVariantUnitRelationIds[] = $itemVariantUnitRelation->id;
                    }

                    $itemVariantUnit->relations()
                        ->withIsLocked()
                        ->whereNotIn('id', $itemVariantUnitRelationIds)
                        ->delete()
                    ;
                }

                // delete itemVariantUnits that are not in the request
                $itemVariant->units()
                    ->withIsLocked()
                    ->whereNotIn('id', $itemVariantUnitIds)
                    ->delete()
                ;

                foreach ($validated['rules'] ?? [] as $ruleId => $rule) {

                    if ($rule['apply_to'] != 'ItemVariant::' . $variant['id']) {
                    continue;
                    }

                    $itemVariantRule = $itemVariant->rules()
                        ->withIsLocked()
                        ->updateOrCreate([
                            'id' => strpos($ruleId, '-') !== false ? null : $ruleId,
                        ], [
                            'item_id' => $item->id,
                            'unit_group_id' => $rule['unit_group_id'],
                            'type' => $rule['type'],
                            'unit_specification' => $rule['unit_specification'],
                            'unit_specification_min' => $rule['unit_specification_min'],
                            'unit_specification_max' => $rule['unit_specification_max'],
                            'pricing_type' => $rule['pricing_type'],
                            'pricing_value' => Str::startsWith($rule['pricing_type'], 'percentage_')
                                ? $rule['pricing_percentage_value']
                                : $rule['pricing_price_value'],
                            'is_active' => $rule['is_active'] == '1' ? true : false,
                        ]);

                    $itemRuleIds[] = $itemVariantRule->id;
                }
            }

            // delete itemRules that are not in the request
            $item->rules()
                ->withIsLocked()
                ->whereNotIn('id', $itemRuleIds)
                ->delete()
            ;

            // delete itemVariants that are not in the request
            $item->variants()
                ->withIsLocked()
                ->whereNotIn('id', $itemVariantIds)
                ->delete()
            ;

            return $item;

        }, 2);

        $this->setMessage($item);

        return to_route('admin.suppliersitems.index', $request->validated()['company_id']);
    }

    public function destroy(
        ?Company $company,
        Item $item
    ): RedirectResponse {
        if ($item->is_locked) {
            $this->setError('The item cannot be deleted.');

            return redirect()->to(url()->previous());
        }

        $companyId = $company?->id ?? $item->company_id;

        $item->delete();

        $this->setMessage($item);

        $previousRoute = app('router')->getRoutes()?->match(request()->create(url()?->previous()))?->getName();

        return Str::endsWith($previousRoute, 'index')
            ? redirect()->to(url()->previous())
            : to_route(preg_replace('/([._])edit$/', '$1index', $previousRoute), $companyId)
        ;
    }
}
