<?php

namespace App\Providers;

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\ServiceProvider;
use Str;
use Vite;

class MacroServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {

        Vite::macro('image', function ($asset) {

            return asset("resources/assets/images/{$asset}");
        });

        Collection::macro('flattenTree', function ($decendantsField) {
            $result = collect();

            foreach ($this->items as $item) {
                $result->push($item);

                if ($item->$decendantsField instanceof Collection) {
                    $result = $result->merge($item->$decendantsField->flattenTree($decendantsField));
                }
            }

            return $result;
        });

        Blueprint::macro('timestampsDefault', function ($percision = 6) {
            $this->timestamp('updated_at', $percision)->nullable();
            $this->timestamp('created_at', $percision)->nullable()->default(DB::raw('CURRENT_TIMESTAMP'));
        });

        Blueprint::macro('systemname', function () {
            return $this->string('systemname', 300);
        });

        Blueprint::macro('salesforceId', function ($unique = true) {
            $column = $this->string('salesforce_id', 100)
                ->nullable()
                ->default(null)
                ->unique();

            if ($unique) {
                $column->unique();
            }

            return $column;
        });

        Blueprint::macro('price', function ($columnName, $decimals = 2) {
            return $this->decimal('price_' . $columnName, $decimals);
        });

        // Blueprint::macro('quantity', function($columnName) {
        //     return $this->unsignedInteger('quantity_'.$columnName);
        // });

        Blueprint::macro('uniqId', function () {

            return $this
                ->string('uniq_id', 32)
                ->unique()
            ;
        });

        foreach ([
            'parentId' => null,
            'domainId' => 'domains',
            'countryId' => 'countries',
            'localeId' => 'locales',
            'addressId' => 'addresses',
            'companyId' => 'companies',
            'userId' => 'users',
            'adminId' => 'admins',

            'itemGroupId' => 'items_groups',

            'itemId' => 'items',
            'itemRelatedId' => 'items',
            'itemUnitId' => 'items_units',
            'itemUnitRelationId' => 'items_units_relations',

            'itemVariantId' => 'items_variants',
            'itemVariantRelatedId' => 'items_variants',
            'itemVariantUnitId' => 'items_variants_units',
            'itemVariantUnitRelationId' => 'items_variants_units_relations',

            'productId' => 'products',
            'categoryId' => 'categories',
            'orderId' => 'orders',
            'eventId' => 'events',
            'warehouseId' => 'warehouses',
            'documentId' => 'documents',
            'settingId' => 'settings',
            'mailId' => 'mails',

            'propertyId' => 'properties',
            'proertyValueId' => 'properties_values',

            'unitId' => 'units',
            'unitTypeId' => 'units_types',
            'unitGroupId' => 'units_groups',

        ] as $foreignKey => $foreignTable) {

            Blueprint::macro($foreignKey, function (
                $onDelete = 'restrict',
                $onUpdate = 'no action') use (
                    $foreignKey,
                    $foreignTable
                ) {

                    return $this->foreignId(Str::snake($foreignKey))
                        ->nullable()
                        ->default(null)
                        ->index()
                        ->references('id')
                        ->on(is_null($foreignTable) ? $this->table : $foreignTable)
                        ->onDelete($onDelete)
                        ->onUpdate($onUpdate);
                });
        }

        Blueprint::macro('ledgerAccount', function (
            $ledgerAccountName,
            $onDelete = 'restrict',
            $onUpdate = 'no action') {

            $this->foreignId('ledger_account_' . strtolower($ledgerAccountName) . '_id')
                ->nullable()
                ->default(null)
                ->index()
                ->references('id')
                ->on('ledgers_accounts')
                ->onDelete($onDelete)
                ->onUpdate($onUpdate)
            ;
        });

        Blueprint::macro('controllable', function ($controller = 'Pages', $method = null) {
            $this->string('controllable_type')->default($controller);
            $this->string('controllable_method')->nullable()->default($method);
        });

        Blueprint::macro('pageable', function ($columns = [], $controller = null) {

            $controller ??= $this->table;

            $this->controllable(str_replace(' ', '', ucwords(str_replace('_', ' ', $this->table))));

            $columns = array_merge([
                'title' => 'string:300',
                'title_tab' => 'string:300',
                'title_sub' => 'string:300',
                'title_custom' => 'string:300',
                'content' => 'text',
                'content_intro' => 'text',
                'content_top' => 'text',
                'content_bottom' => 'text',
                'meta_description' => 'text',
             //   'meta_keywords' => 'text',
            ], $columns);

            foreach ($columns as $name => $data) {

                $data = explode(':', $data);

                foreach (config('locales') as $locale) {

                    if ($data[0] == 'string') {
                        $length = $data[1] ?? 255;
                        $this->string($name . '_' . $locale['code'], $length)->nullable()->default(null);
                    }

                    if ($data[0] == 'text') {
                        $this->text($name . '_' . $locale['code'])->nullable()->default(null);
                    }
                }
            }

            $this->is('auth', false);
            $this->is('indexable', true);

            $this->is('active');
            $this->is('active', true);
        });

        Blueprint::macro('translatable', function (
            $columnName,
            $maxLength = 255,
            $defaultValue = null,
            $columnType = 'string',
            $nullable = true) {

            foreach (config('locales') as $code => $lang) {

                if ($columnType == 'string') {
                    $column = $this->string($columnName . '_' . $code, $maxLength);
                }

                if ($columnType == 'text') {
                    $column = $this->text($columnName . '_' . $code);
                }

                $column->default($defaultValue);

                if ($nullable || (is_null($nullable) && is_null($defaultValue))) {
                    $column->nullable();
                }
            }
        });

        Blueprint::macro('at', function ($dateName, $default = null) {
            $this->dateTime($dateName . '_at')
                ->nullable()
                ->default($default)
                ->index();
        });

        Blueprint::macro('is', function ($columnName, $locales = false, $defaultValue = 0) {
            $this->switch('is_' . $columnName, $locales, $defaultValue);
        });

        Blueprint::macro('in', function ($columnName, $locales = false, $defaultValue = 0) {
            $this->switch('in_' . $columnName, $locales, $defaultValue);
        });

        Blueprint::macro('can', function ($columnName, $locales = false, $defaultValue = 0) {
            $this->switch('can_' . $columnName, $locales, $defaultValue);
        });

        Blueprint::macro('has', function ($columnName, $locales = false, $defaultValue = 0) {
            $this->switch('has_' . $columnName, $locales, $defaultValue);
        });

        Blueprint::macro('switch', function ($columnName, $locales = false, $defaultValue = 0) {

            if ($locales) {
                foreach (config('locales') as $locale) {
                    $this->boolean($columnName . '_' . $locale['code'])->default($defaultValue)->unsigned();
                }

                return;
            }

            $this->boolean($columnName)->default($defaultValue)->unsigned();
        });

        Blueprint::macro('sortColumns', function () {

            $this->columns = collect($this->columns)->sortBy(function ($column) {

                $name = $column['name'];

                // Sorting priority
                $priority = 150;

                if ($name === 'id') {
                    $priority = 1;
                } elseif ($name === 'parent_id') {
                    $priority = 10;
                } elseif ($name === 'domain_id') {
                    $priority = 20;
                } elseif ($name === 'locale_id') {
                    $priority = 30;
                } elseif ($name === 'country_id') {
                    $priority = 40;
                } elseif ($name === 'admin_id') {
                    $priority = 50;
                } elseif ($name === 'user_id') {
                    $priority = 60;
                } elseif ($name === 'companyId') {
                    $priority = 70;
                } elseif (str_ends_with($name, '_id')) {
                    $priority = 100;
                } elseif ($name === 'systemname') {
                    $priority = 120;
                } elseif (str_starts_with($name, 'has_')) {
                    $priority = 200;
                } elseif (str_starts_with($name, 'can_')) {
                    $priority = 300;
                } elseif (str_starts_with($name, 'in_')) {
                    $priority = 400;
                } elseif (str_starts_with($name, 'is_')) {

                    if ($name === 'is_active') {
                        $priority = 600;
                    } elseif (str_starts_with($name, 'is_active')) {
                        $priority = 500;
                    } else {
                        $priority = 450;
                    }

                } elseif (str_ends_with($name, '_at')) {
                    if ($name === 'created_at') {
                        $priority = 1004;
                    } elseif ($name === 'updated_at') {
                        $priority = 1003;
                    } elseif ($name === 'deleted_at') {
                        $priority = 1002;
                    } else {
                        $priority = 1001;
                    }
                }

                return $priority;
            })->values()->all();
        });

        Blueprint::macro('title', function () {
            foreach (config('languages') as $langCode => $lang) {
                $this->string('title_' . $langCode, 100)->nullable()->default(null);
            }
        });

        Blueprint::macro('titleConfigurator', function () {
            foreach (config('languages') as $langCode => $lang) {
                $this->string('title_configurator_' . $langCode, 100)->nullable()->default(null);
            }
        });

        Blueprint::macro('titleSub', function () {
            foreach (config('languages') as $langCode => $lang) {
                $this->string('title_sub_' . $langCode, 255)->nullable()->default(null);
            }
        });

        Blueprint::macro('titleTab', function () {
            foreach (config('languages') as $langCode => $lang) {
                $this->string('title_tab_' . $langCode, 250)->nullable()->default(null);
            }
        });

        Blueprint::macro('content', function () {
            foreach (config('languages') as $langCode => $lang) {
                $this->text('content_' . $langCode)->default('')->nullable()->default(null);
            }
        });

        Blueprint::macro('sort', function ($columnName = 'sort', $locales = false, $defaultValue = 0) {

            $columnName = $columnName != 'sort' ? 'sort_' . $columnName : $columnName;

            $this->integer($columnName)->default($defaultValue);
        });
    }
}
