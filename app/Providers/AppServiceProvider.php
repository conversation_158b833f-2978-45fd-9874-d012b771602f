<?php

namespace App\Providers;

use App\Services\APIS\GoogleAIPlatformAPIService;
use App\Services\APIS\GoogleDocumentAIAPIService;
use App\Services\APIS\SalesForceAPIService;
use App\View\Components\Admin\Component\Alert;
use App\View\Components\Admin\Component\Flag;
use App\View\Components\Admin\Component\Panel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;
use Illuminate\View\DynamicComponent;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {

        if (in_array($this->app->environment(), ['local'])) {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
          //  $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
        }

        $this->app->register(\GeneaLabs\LaravelModelCaching\Providers\Service::class);

        $this->app->singleton(GoogleAIPlatformAPIService::class, function ($app) {
            return new GoogleAIPlatformAPIService();
        });

        $this->app->singleton(GoogleDocumentAIAPIService::class, function ($app) {
            return new GoogleDocumentAIAPIService();
        });

        $this->app->singleton(SalesForceAPIService::class, function ($app) {
            return new SalesForceAPIService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Model::shouldBeStrict();
        Model::preventLazyLoading();
       // Model::automaticallyEagerLoadRelationships();

        // Telescope::night();

        Vite::prefetch(concurrency: 10);

        Blade::component('dynamic-component', DynamicComponent::class);

        $this->loadViewsFrom(resource_path('admin/views'), 'admin');
        $this->loadViewsFrom(resource_path('portal/views'), 'portal');

        $this->loadTranslationsFrom(resource_path('lang/en/enums'), 'enums');

        Route::macro('obtainParametersFromCurrentRoute', function (
            string $routeName,
            ?string $currentRouteName = null
        ): ?array {
            $currentRouteName ??= Route::currentRouteName();

            if (is_null($currentRouteName)) {
                return null;
            }

            $parameters = array_map(
                function ($parameter) {
                    return Route::getCurrentRoute()->parameter($parameter) ?? null;
                },
                Route::getRoutes()->getByName($routeName)->parameterNames() ?? []
            );

            return array_filter($parameters);
        });

        Blade::component('admin.panel', Panel::class);
        Blade::component('admin.flag', Flag::class);
        Blade::component('admin.alert', Alert::class);
        Blade::component('admin.link.sortable', \App\View\Components\Admin\Component\LinkSortable::class);

        Blade::directive('money', function ($arguments) {
            $args = array_map(function ($a) {
                return trim($a);
            }, explode(',', $arguments));

            foreach ([
                'amount' => null,
                'currency' => "'EUR'",
                'formatTo' => "'nl_NL'",
            ] as $var => $default) {

                $$var = $default;

                if (isset($args[0])) {
                    $$var = array_shift($args);
                }
            }

            return "<?php
                if (is_null(" . $amount . ")) {
                    echo '-';
                }   
                else {
                    echo Brick\Money\Money::of(" . $amount . ", " . $currency . ")->formatTo(" . $formatTo . ");
                }            
            ?>";
        });

        Blade::directive('orderBadge', function ($arguments) {
            $args = array_map('trim', explode(',', $arguments));

            $state = $args[0];
            $label = $args[1] ?? 'null';
            $locale = $args[2] ?? "'" . App::getLocale() . "'";

            return '<?php echo App\Services\BladeDirectivesService::orderBadge(' . $state . ', ' . $label . ', ' . $locale . '); ?>';
        });

        Blade::directive('name', function ($arguments) {
            $args = array_map('trim', explode(',', $arguments));

            $data = $args[0] ?? 'new BaseModel()';
            $column = $args[1] ?? 'name';
            $fallbackColumns = $args[2] ?? "['systemname', 'name']";
            $locale = $args[3] ?? 'null';

            return '<?php echo App\Services\BladeDirectivesService::name(' . $data . ', ' . $column . ', ' . $fallbackColumns . ', ' . $locale . '); ?>';
        });

        Blade::directive('countryName', function ($arguments) {
            $args = array_map(function ($a) {
                return trim($a);
            }, explode(',', $arguments));

            foreach ([
                'selector' => null,
                'locale' => App::getLocale(),
            ] as $var => $default) {

                $$var = $default;

                if (isset($args[0])) {
                    $$var = array_shift($args);
                }
            }

            if (str_starts_with($locale, '$')) {
                $column = "'name_'.\$locale";
            } else {
                $locale = trim(trim($locale, '"'), "'");
                $column = "'name_$locale'";
            }

            return "<?php
                echo App\Models\Country::where(
                    is_int(" . $selector . ")
                        ? 'id'
                        : 'code', 
                    " . $selector . ")->select(" . $column . ")->first()->{" . $column . "};   

            ?>";
        });

        Validator::extendImplicit('mustbe_if', function ($attribute, $value, $parameters, $validator) {

            // Ensure the parameters are provided
            if (count($parameters) != 3) {
                return false;
            }

            // Get the data being validated
            $data = $validator->getData();

            // Check if the other field has the required value
            if (isset($data[$parameters[1]]) &&
                $data[$parameters[1]] == $parameters[2]) {
                return $value === $parameters[0];
            }

            // Otherwise, no specific requirement on this field
            return true;
        });

        Validator::replacer('mustbe_if', function ($message, $attribute, $rule, $parameters) {
            return str_replace([':attribute', ':value', ':other', ':othervalue'], [$attribute, $parameters[0], $parameters[1], $parameters[2]], 'The :attribute must be :value if :other is :othervalue.');
        });

        Validator::extend('cannotbe_if', function ($attribute, $value, $parameters, $validator) {

            // Ensure the parameters are provided
            if (count($parameters) != 3) {
                return false;
            }

            // Get the data being validated
            $data = $validator->getData();

            // Check if the other field has the required value
            if (isset($data[$parameters[1]]) &&
                $data[$parameters[1]] == $parameters[2]) {
                return $value !== $parameters[0];
            }

            // Otherwise, no specific requirement on this field
            return true;
        });

        Validator::replacer('cannotbe_if', function ($message, $attribute, $rule, $parameters) {
            return str_replace([':attribute', ':value', ':other', ':othervalue'], [$attribute, $parameters[0], $parameters[1], $parameters[2]], 'The :attribute cannot be :value if :other is :othervalue.');
        });

        Validator::extendImplicit('required_if_all', function ($attribute, $value, $parameters, $validator) {

            if (count($parameters) <= 1) {
                return false;
            }

            $data = $validator->getData();

            $criteriaMet = true;
            foreach ($parameters as $param) {

                [$key, $val] = explode('=', $param);

                \Log::debug([$key, $val]);

                if ($data[$key] ?? $val !== null) {
                    $criteriaMet = false;
                }
            }

            if ($criteriaMet) {
                return isset($data[$attribute]);
            }

            return true;
        });

        Validator::replacer('required_if_all', function ($message, $attribute, $rule, $parameters) {

            return str_replace(
                [':attribute', ':value'],
                [$attribute, $parameters[0]],
                'The :attribute must be :value if MULTIPLE OTHERS.'
            );
        });

        Validator::extendImplicit('mustbe_if_all', function ($attribute, $value, $parameters, $validator) {

            if (count($parameters) <= 2) {
                return false;
            }

            $data = $validator->getData();

            $valueMustBe = array_shift($parameters);

            $criteriaMet = true;
            foreach ($parameters as $param) {

                [$key, $val] = explode('=', $param);

                \Log::debug([$key, $val]);

                if ($data[$key] ?? $val !== null) {
                    $criteriaMet = false;
                }
            }

            if ($criteriaMet) {
                return $valueMustBe === $val;
            }

            return true;
        });

        Validator::replacer('mustbe_if_all', function ($message, $attribute, $rule, $parameters) {
            return str_replace([':attribute', ':value', ':other', ':othervalue'], [$attribute, $parameters[0], $parameters[1], $parameters[2]], 'The :attribute must be :value if :other is :othervalue.');
        });

        Validator::extendImplicit('required_if_not', function ($attribute, $value, $parameters, $validator) {

            if (count($parameters) != 2) {
                return false;
            }

            $data = $validator->getData();

            if (! isset($data[$parameters[0]])) {
                return false;
            }

            if ($data[$parameters[0]] !== $parameters[1]) {
                return isset($value);
            }

            return true;
        });

        Validator::replacer('required_if_not', function ($message, $attribute, $rule, $parameters) {
            return str_replace([':attribute'], [$attribute], 'The :attribute is required.');
        });

        Validator::extend('format_money', function ($attribute, $value, $parameters, $validator) {

            $value = normalizePrice(
                $value,
                $parameters[0] ?? 2
            );

            return is_numeric($value);
        });

        Relation::enforceMorphMap((array) Cache::remember('morphmap', 60 * 60, function () {
            $models = collect(glob(app_path('Models') . '/*.php'))->map(function ($file) {
                return basename($file, '.php');
            })->toArray();

            $relations = [];
            foreach ($models as $model) {
                $relations[$model] = 'App\\Models\\' . $model;
            }

            return $relations;
        }));

        Password::defaults(function () {

            if ($this->app->environment() != 'production') {
                return Password::min(1);
            }

            return Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols();
        });
    }
}

 // View::share([
        //     'app' => App::get('name'),
        //     'agent' => new Agent(),
        //     'message' => session('message') ?? null
        // ]);

                // View::addNamespace(
        //     App::get('name'),
        //     resource_path(strtolower(App::get('name')).'/views/')
        // );

       // View::addLocation(resource_path(strtolower(App::get('name')).'/views'));

 //   Validator::replacer('greater_than_field', function($message, $attribute, $rule, $parameters) {
        //     return str_replace(':field', $parameters[0], $message);
        //   });

        //   Validator::extend('greater_than_field', function($attribute, $value, $parameters, $validator) {
        //     $min_field = $parameters[0];
        //     $data = $validator->getData();
        //     $min_value = $data[$min_field];
        //     return $value > $min_value;
        //   });

        //   Validator::replacer('greater_than_field', function($message, $attribute, $rule, $parameters) {
        //     return str_replace(':field', $parameters[0], $message);
        //   });
        // bind data to all views
        // view()->composer('*', function ($view)
        // {
        //     $view->with('module', 'fddsf');

        // });

        // $this->app->extend('view', function (ViewFactory $viewFactory, $app)
        // {
        //     return new CustomViewFactory(
        //         $viewFactory->getEngineResolver(),
        //         $viewFactory->getFinder(),
        //         $app->make(Dispatcher::class)
        //     );
        // });
