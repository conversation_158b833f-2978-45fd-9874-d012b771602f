<?php

declare(strict_types=1);

namespace App\Services;

use App\Contracts\TransformerContract;
use Illuminate\Contracts\Validation\Validator as ValidatorContract;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\MessageBag;

final class MapValidate
{
    public private(set) MessageBag $errors;

    public function __construct(
        private string $keyPrefix,
        private array $inputData = [],
        /** @var array{key: string, rules: array, before?: array, default?: mixed, value_if_invalid?: mixed} */
        private array $mappings = [],
    ) {
        $this->errors = new MessageBag();
    }

    private function validateMapping(): ValidatorContract
    {
        $rules = [
            '*' => [
                'required',
                'array',
            ],
            '*.key' => [
                'required',
                'string',
            ],
            '*.rules' => [
                'required',
                'array',
            ],
            '*.before' => [
                'nullable',
                'array',
            ],
            '*.before.*' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if (! class_exists($value)) {
                        $fail('The before mapping must be a valid class.');

                        return;
                    }

                    if (! in_array(TransformerContract::class, class_implements($value), true)) {
                        $fail('The before mapping must implement the TransformerContract.');
                    }
                },
            ],
            '*.default' => [
                'nullable',
            ],
            '*.value_if_invalid' => [
                'nullable',
            ],
        ];

        return Validator::make(data: $this->mappings, rules: $rules, messages: [
            '*.before.*.string' => 'The before mapping must be a valid class.',
        ]);
    }

    public function validated(): array
    {
        if ($this->validateMapping()->fails()) {
            $this->errors->merge($this->validateMapping()->errors());

            return [];
        }

        $rulesData = [];
        foreach ($this->mappings as $attribute => $mapping) {

            $key = $mapping['key'];

            if (is_null($key)) {
                continue;
            }

            $value = data_get(
                $this->inputData,
                $this->keyPrefix . $key,
                $mapping['default'] ?? null
            );

            foreach (data_get($mapping, 'before', []) as $before) {

                if (! is_string($before) || ! class_exists($before)) {
                    continue;
                }

                $before = new $before();

                if ($before->canTransform($value)) {
                    $value = $before->transform($value);
                }
            }

            $value ??= $mapping['default'] ?? null;

            if (($mapping['value_if_invalid'] ?? null) &&
                ! $this->isValidAttribute($attribute, $value, $mapping['rules'])) {
                $value = $mapping['value_if_invalid'];
            }

            $this->inputData[$attribute] = $value;
            $rulesData[$attribute] = $mapping['rules'];
        }

        return $this->validateAttributes(
            $this->inputData,
            $rulesData
        );
    }

    protected function validateAttributes(array $data, array $rules)
    {
        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            $this->errors = $validator->errors();

            return [];
        }

        return $validator->validated();
    }

    protected function isValidAttribute($attribute, $value, $rules)
    {
        $validator = Validator::make([
            $attribute => $value,
        ], [
            $attribute => $rules,
        ]);

        return ! $validator->passes();
    }
}
