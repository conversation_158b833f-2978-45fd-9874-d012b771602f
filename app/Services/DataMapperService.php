<?php

namespace App\Services;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Arr;
use Illuminate\Support\MessageBag;
use stdClass;

class DataMapperService
{
    public MessageBag $errors;

    protected array $validatedData = [];

    public function __construct(
        public array $mappings,
        Arrayable|stdClass $inputData,
        public string $keyPrefix = ''
    ) {
        $data = [];
        if ($inputData instanceof Arrayable) {
            $data = $inputData->toArray();
        }

        if ($inputData instanceof stdClass) {
            $data = (array) $inputData;
        }

        $mapper = new MapValidate($this->keyPrefix, Arr::dot($data), $this->mappings);

        $this->validatedData = $mapper->validated();
        $this->errors = $mapper->errors ?? new MessageBag();
    }

    public function validated(): array
    {
        return $this->validatedData;
    }

    public function hasErrors(): bool
    {
        return $this->errors->count() > 0;
    }
}
