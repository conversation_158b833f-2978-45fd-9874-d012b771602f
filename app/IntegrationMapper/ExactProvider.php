<?php

declare(strict_types=1);

namespace App\IntegrationMapper;

final class ExactProvider implements ProviderContract
{
    private ExactProviderData $data;

    public function fromArray($data): self
    {
        $this->data = new ExactProviderData(
            name: $data['name'],
            email: $data['email'],
            phone: $data['phone'],
            country: $data['country'],
            city: $data['city'],
            postalCode: $data['postalCode'],
            address: $data['address'],
            debitorNumber: $data['debitorNumber'],
            creditorNumber: $data['creditorNumber'],
        );

        return $this;
    }

    public function toDatabase(): array
    {
        return [
            ...$this->data->toArray(),
        ];
    }
}
