<?php

declare(strict_types=1);

namespace App\Data;

use App\Contracts\TransformerContract;
use <PERSON><PERSON>\LaravelData\Dto;

final class Mapping extends Dto
{
    public function __construct(
        public string $localKey,
        public string $remoteKey,
        public array $rules,
        public array $before = [],
        public mixed $default = null,
        public mixed $value_if_invalid = null,
        private readonly string $keyPrefix = ''
    ) {
        //
    }

    public static function rules(): array
    {
        return [
            'localKey' => [
                'required',
                'string',
            ],
            'remoteKey' => [
                'required',
                'string',
            ],
            'rules' => [
                'required',
                'array',
            ],
            'before' => [
                'array',
            ],
            'before.*' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    if (! class_exists($value)) {
                        $fail('The before mapping must be a valid class.');

                        return;
                    }

                    if (! in_array(TransformerContract::class, class_implements($value), true)) {
                        $fail('The before mapping must implement the TransformerContract.');
                    }
                },
            ],
            'default' => [
                'nullable',
            ],
            'value_if_invalid' => [
                'nullable',
            ],
        ];
    }

    public function transform()
    {

        $value = data_get(
            $this->inputData,
            $this->keyPrefix . $this->localKey,
            $this->default ?? null
        );

        foreach ($this->before as $before) {

            if (! is_string($before) || ! class_exists($before)) {
                continue;
            }

            $before = new $before();

            if ($before->canTransform($value)) {
                $value = $before->transform($value);
            }
        }

        $value ??= $mapping['default'] ?? null;

        if (($mapping['value_if_invalid'] ?? null) &&
            ! $this->isValidAttribute($attribute, $value, $mapping['rules'])) {
            $value = $mapping['value_if_invalid'];
        }

        $this->inputData[$attribute] = $value;
    }
}
