<?php

namespace App\Transformers;

use App\Contracts\TransformerContract;
use App\Models\Country;

class CountryCodeToCountryIdTransformer extends Transformer implements TransformerContract
{
    public function canTransform($value): bool
    {
        return $this->canTransform = is_string($value);
    }

    public function transform($value): ?string
    {
        if (is_null($this->canTransform)) {
            $this->canTransform($value);
        }

        if (! $this->canTransform) {
        return null;
        }

        $value = trim($value);

        $attribute = strlen($value) === 2
            ? 'code'
            : 'iso3'
        ;

        return Country::query()
            ->where($attribute, strtoupper(trim($value)))
            ?->first()?->id
        ;
    }
}
