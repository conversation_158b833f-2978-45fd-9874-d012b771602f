<?php

namespace App\Transformers;

use App\Contracts\TransformerContract;
use App\Models\Country;

class CountryCodeToLocaleIdTransformer extends Transformer implements TransformerContract
{
    public function canTransform($value): bool
    {
        return $this->canTransform = is_string($value);
    }

    public function transform($value): ?string
    {
        if (is_null($this->canTransform)) {
            $this->canTransform($value);
        }

        if (! $this->canTransform) {
        return null;
        }

        $value = trim($value);

        if (strlen($value) === 3) {
            $value = Country::query()
                ->where('iso3', strtoupper($value))
                ?->first()?->code;

        }

        if (strlen($value) === 2) {
            return collect(config('locales.' . $value) ?? [])->get('id');
        }

        return null;
    }
}
