<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies_addresses_users', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('company_address_id');
            $table->foreign('company_address_id')
                ->references('id')
                ->on('companies_addresses')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')
                ->references('id')
                ->on('users')
                ->onDelete('restrict')
                ->onUpdate('no action');

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies_addresses_users');
    }
};
