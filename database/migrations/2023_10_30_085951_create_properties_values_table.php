<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('properties_values', function (Blueprint $table) {

            $table->id();

            $table->foreignId('property_id')
                ->references('id')
                ->on('properties')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->systemname();

            $table->translatable('name');
            $table->translatable('content', columnType: 'text');
            $table->string('value', 300)->nullable()->default(null);

            $table->is('active');
            $table->is('active', true);

            $table->integer('sort')->nullable();

            $table->timestampsDefault();
            $table->softDeletes();
            $table->sortColumns();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('properties_values');
    }
};
