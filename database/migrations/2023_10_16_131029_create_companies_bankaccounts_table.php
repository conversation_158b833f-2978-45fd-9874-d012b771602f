<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies_bankaccounts', function (Blueprint $table) {

            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            $table->id();
            $table->companyId();
            $table->string('bank_name', 400)->nullable()->default(null);
            $table->string('bank_code', 50)->nullable()->default(null);
            $table->string('bank_branch_code', 50)->nullable()->default(null);
            $table->string('number', 50);
            $table->string('iban', 50)->nullable()->default(null);
            $table->string('bic', 50)->nullable()->default(null);
            $table->string('swift', 50)->nullable()->default(null);

            $table->is('verified');

            $table->softDeletes();
            $table->timestampsDefault();

            $table->unique(['company_id', 'number'], 'companies_bankaccounts_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies_bankaccounts');
    }
};
