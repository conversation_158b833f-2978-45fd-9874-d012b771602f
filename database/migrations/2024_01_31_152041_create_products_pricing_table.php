<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products_pricing', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('product_id');
            $table->foreign('product_id')
                ->references('id')
                ->on('products')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->unsignedSmallInteger('sector_id')->nullable()->index();

            $table->string('country', 2)->default('nl')->index();
            $table->string('groupcode', 1)->nullable()->index();
            $table->enum('unit', ['M1', 'M2', 'PIECE']);
            $table->decimal('price', 10, 5)->nullable()->index();
            $table->decimal('price_back', 10, 5)->nullable();
            $table->unsignedInteger('minimal_m2')->nullable();
            $table->unsignedInteger('minimal_quantity')->nullable();
            $table->boolean('status', 1)->default(0)->unsigned();

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products_pricing');
    }
};
