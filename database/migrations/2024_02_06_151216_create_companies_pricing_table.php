<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies_pricing', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('company_id');

            $table->foreign('company_id')
                ->references('id')
                ->on('companies')
                ->onDelete('cascade')
                ->onUpdate('no action');

            $table->unsignedInteger('product_substrate_id')->index()->nullable();
            $table->unsignedInteger('product_convection_id')->index()->nullable();

            $table->enum('type', ['substrate','confection','substrate_confection','confection_substrate'])->default('substrate');
            $table->enum('conditioning', ['always','minimal','maximal','range','dimensions'])->nullable();
            $table->enum('unit', ['M1', 'M2', 'PIECE'])->nullable();

            $table->integer('amount_start')->unsigned()->nullable();
            $table->integer('amount_stop')->unsigned()->nullable();
            $table->enum('discount', ['percentage', 'fixed'])->nullable();
            $table->enum('direction', ['+', '-'])->nullable();

            $table->decimal('value', 10, 2)->nullable();
            $table->decimal('value_back', 10, 2)->nullable();

            $table->timestampsDefault();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies_pricing');
    }
};
