parameters:
	ignoreErrors:
		-
			message: '#^Call to static method camel\(\) on an unknown class Str\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/APIService.php

		-
			message: '#^Call to static method error\(\) on an unknown class Log\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/APIService.php

		-
			message: '#^Call to static method get\(\) on an unknown class Storage\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/APIService.php

		-
			message: '#^Call to static method info\(\) on an unknown class Log\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/APIService.php

		-
			message: '#^Call to an undefined static method App\\Models\\LedgerAccount\:\:firstOrCreate\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Services/APIS/ExactGlobeAPIService.php

		-
			message: '#^Call to an undefined static method App\\Models\\LedgerCategory\:\:firstOrCreate\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Services/APIS/ExactGlobeAPIService.php

		-
			message: '#^Call to static method connection\(\) on an unknown class DB\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/APIS/ExactGlobeAPIService.php

		-
			message: '#^Call to static method isAssoc\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/ExactGlobeAPIService.php

		-
			message: '#^Call to static method undot\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/ExactGlobeAPIService.php

		-
			message: '#^Variable \$operator might not be defined\.$#'
			identifier: variable.undefined
			count: 1
			path: app/Services/APIS/ExactGlobeAPIService.php

		-
			message: '#^Call to static method dot\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/GoogleAIPlatformAPIService.php

		-
			message: '#^Call to static method get\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 9
			path: app/Services/APIS/GoogleAIPlatformAPIService.php

		-
			message: '#^Call to static method has\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/APIS/GoogleAIPlatformAPIService.php

		-
			message: '#^Call to static method undot\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/APIS/GoogleAIPlatformAPIService.php

		-
			message: '#^Call to static method get\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 3
			path: app/Services/APIS/GoogleDocumentAIAPIService.php

		-
			message: '#^Deprecated in PHP 8\.4\: Parameter \#4 \$locale \(string\) is implicitly nullable via default value null\.$#'
			identifier: parameter.implicitlyNullable
			count: 1
			path: app/Services/BladeDirectivesService.php

		-
			message: '#^Call to static method isAssociative\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 3
			path: app/Services/BladeService.php

		-
			message: '#^Call to static method mergeByIndex\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/BladeService.php

		-
			message: '#^Call to static method mergeByKey\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/BladeService.php

		-
			message: '#^Call to static method replaceFirst\(\) on an unknown class Str\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/BladeService.php

		-
			message: '#^Call to static method replaceStart\(\) on an unknown class Str\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/BladeService.php

		-
			message: '#^Call to static method startsWith\(\) on an unknown class Str\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/BladeService.php

		-
			message: '#^Call to static method dot\(\) on an unknown class Arr\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/DataMapperService.php

		-
			message: '#^Call to static method make\(\) on an unknown class Validator\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/DataMapperService.php

		-
			message: '#^Call to an undefined static method App\\Models\\Document\:\:updateOrCreate\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Services/DocumentService.php

		-
			message: '#^Call to static method isLocal\(\) on an unknown class App\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/DocumentService.php

		-
			message: '#^Call to static method path\(\) on an unknown class Process\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/DocumentService.php

		-
			message: '#^Call to static method replaceLast\(\) on an unknown class Str\.$#'
			identifier: class.notFound
			count: 2
			path: app/Services/DocumentService.php

		-
			message: '#^Call to static method run\(\) on an unknown class Concurrency\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/DocumentService.php

		-
			message: '#^Call to static method run\(\) on an unknown class Process\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/DocumentService.php

		-
			message: '#^Call to an undefined static method App\\Models\\Mail\:\:create\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Call to static method environment\(\) on an unknown class App\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Call to static method makeDirectory\(\) on an unknown class Storage\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Call to static method put\(\) on an unknown class Storage\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Call to static method user\(\) on an unknown class Auth\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Deprecated in PHP 8\.4\: Parameter \#2 \$mailable \(string\) is implicitly nullable via default value null\.$#'
			identifier: parameter.implicitlyNullable
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Deprecated in PHP 8\.4\: Parameter \#3 \$relatableModel \(Illuminate\\Database\\Eloquent\\Model\) is implicitly nullable via default value null\.$#'
			identifier: parameter.implicitlyNullable
			count: 1
			path: app/Services/MailService.php

		-
			message: '#^Call to static method json\(\) on an unknown class Storage\.$#'
			identifier: class.notFound
			count: 1
			path: app/Services/PurchaseOrderService.php

		-
			message: '#^Deprecated in PHP 8\.4\: Parameter \#2 \$state \(App\\Enums\\OrderState\) is implicitly nullable via default value null\.$#'
			identifier: parameter.implicitlyNullable
			count: 1
			path: app/Services/PurchaseOrderService.php

		-
			message: '#^Call to an undefined static method App\\Models\\Slug\:\:where\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Services/SlugService.php

		-
			message: '#^Call to an undefined static method App\\Models\\TaxIdentifier\:\:firstOrCreate\(\)\.$#'
			identifier: staticMethod.notFound
			count: 1
			path: app/Services/TaxIndentificationService.php
